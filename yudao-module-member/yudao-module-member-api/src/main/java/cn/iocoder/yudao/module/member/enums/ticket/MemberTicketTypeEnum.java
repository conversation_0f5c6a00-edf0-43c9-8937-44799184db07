package cn.iocoder.yudao.module.member.enums.ticket;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum MemberTicketTypeEnum implements IntArrayValuable {
    ORDER(1, "订单问题"),
    PRODUCT(2, "商品问题"),
    PACKAGE(3, "包裹问题"),
    LOGISTICS(4, "物流问题"),
    ACCOUNT(5, "账户问题"),
    OTHER(6, "其他问题");

    private final Integer type;
    private final String name;

    @Override
    public int[] array() {
        return new int[0];
    }
}