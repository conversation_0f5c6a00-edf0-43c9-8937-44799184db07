package cn.iocoder.yudao.module.member.enums.ticket;


import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum MemberTicketStatusEnum implements IntArrayValuable {
    PENDING(1, "待处理"),
    PROCESSING(2, "处理中"),
    WAITING_USER(3, "待用户回复"),
    RESOLVED(4, "已解决"),
    CLOSED(5, "已关闭");

    private final Integer status;
    private final String name;


    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(MemberTicketStatusEnum::getStatus).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }
}