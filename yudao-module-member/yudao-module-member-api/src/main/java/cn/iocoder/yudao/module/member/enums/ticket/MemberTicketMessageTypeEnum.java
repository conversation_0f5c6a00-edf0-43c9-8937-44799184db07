package cn.iocoder.yudao.module.member.enums.ticket;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum MemberTicketMessageTypeEnum implements IntArrayValuable {
    USER(1, "用户回复"),
    ADMIN(2, "客服回复");

    private final Integer type;
    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(MemberTicketMessageTypeEnum::getType).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }
}