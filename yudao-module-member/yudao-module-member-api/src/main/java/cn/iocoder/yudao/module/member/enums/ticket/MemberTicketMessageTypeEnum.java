package cn.iocoder.yudao.module.member.enums.ticket;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum MemberTicketMessageTypeEnum implements IntArrayValuable {
    USER(1, "用户回复"),
    ADMIN(2, "客服回复");

    private final Integer type;
    private final String name;

    @Override
    public int[] array() {
        return new int[0];
    }
}