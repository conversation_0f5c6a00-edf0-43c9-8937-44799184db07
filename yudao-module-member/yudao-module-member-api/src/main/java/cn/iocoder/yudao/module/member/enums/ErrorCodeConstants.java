package cn.iocoder.yudao.module.member.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * Member 错误码枚举类
 * <p>
 * member 系统，使用 1-004-000-000 段
 */
public interface ErrorCodeConstants {

    //// ========== 用户相关  1-004-001-000 ============
    //ErrorCode USER_NOT_EXISTS = new ErrorCode(1_004_001_000, "用户不存在");
    //ErrorCode USER_MOBILE_NOT_EXISTS = new ErrorCode(1_004_001_001, "手机号未注册用户");
    //ErrorCode USER_MOBILE_USED = new ErrorCode(1_004_001_002, "修改手机失败，该手机号({})已经被使用");
    //ErrorCode USER_POINT_NOT_ENOUGH = new ErrorCode(1_004_001_003, "用户积分余额不足");
    //
    //// ========== AUTH 模块 1-004-003-000 ==========
    //ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_004_003_000, "登录失败，账号密码不正确");
    //ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_004_003_001, "登录失败，账号被禁用");
    //ErrorCode AUTH_SOCIAL_USER_NOT_FOUND = new ErrorCode(1_004_003_005, "登录失败，解析不到三方登录信息");
    //ErrorCode AUTH_MOBILE_USED = new ErrorCode(1_004_003_007, "手机号已经被使用");
    //
    //// ========== 用户收件地址 1-004-004-000 ==========
    //ErrorCode ADDRESS_NOT_EXISTS = new ErrorCode(1_004_004_000, "用户收件地址不存在");
    //
    ////========== 用户标签 1-004-006-000 ==========
    //ErrorCode TAG_NOT_EXISTS = new ErrorCode(1_004_006_000, "用户标签不存在");
    //ErrorCode TAG_NAME_EXISTS = new ErrorCode(1_004_006_001, "用户标签已经存在");
    //ErrorCode TAG_HAS_USER = new ErrorCode(1_004_006_002, "用户标签下存在用户，无法删除");
    //
    ////========== 积分配置 1-004-007-000 ==========
    //
    ////========== 积分记录 1-004-008-000 ==========
    //ErrorCode POINT_RECORD_BIZ_NOT_SUPPORT = new ErrorCode(1_004_008_000, "用户积分记录业务类型不支持");
    //
    ////========== 签到配置 1-004-009-000 ==========
    //ErrorCode SIGN_IN_CONFIG_NOT_EXISTS = new ErrorCode(1_004_009_000, "签到天数规则不存在");
    //ErrorCode SIGN_IN_CONFIG_EXISTS = new ErrorCode(1_004_009_001, "签到天数规则已存在");
    //
    ////========== 签到配置 1-004-010-000 ==========
    //ErrorCode SIGN_IN_RECORD_TODAY_EXISTS = new ErrorCode(1_004_010_000, "今日已签到，请勿重复签到");
    //
    ////========== 用户等级 1-004-011-000 ==========
    //ErrorCode LEVEL_NOT_EXISTS = new ErrorCode(1_004_011_000, "用户等级不存在");
    //ErrorCode LEVEL_NAME_EXISTS = new ErrorCode(1_004_011_001, "用户等级名称[{}]已被使用");
    //ErrorCode LEVEL_VALUE_EXISTS = new ErrorCode(1_004_011_002, "用户等级值[{}]已被[{}]使用");
    //ErrorCode LEVEL_EXPERIENCE_MIN = new ErrorCode(1_004_011_003, "升级经验必须大于上一个等级[{}]设置的升级经验[{}]");
    //ErrorCode LEVEL_EXPERIENCE_MAX = new ErrorCode(1_004_011_004, "升级经验必须小于下一个等级[{}]设置的升级经验[{}]");
    //ErrorCode LEVEL_HAS_USER = new ErrorCode(1_004_011_005, "用户等级下存在用户，无法删除");
    //
    //ErrorCode EXPERIENCE_BIZ_NOT_SUPPORT = new ErrorCode(1_004_011_201, "用户经验业务类型不支持");
    //
    ////========== 用户分组 1-004-012-000 ==========
    //ErrorCode GROUP_NOT_EXISTS = new ErrorCode(1_004_012_000, "用户分组不存在");
    //ErrorCode GROUP_HAS_USER = new ErrorCode(1_004_012_001, "用户分组下存在用户，无法删除");

    // ========== 用户相关  1-004-001-000 ============
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_004_001_000, "member.user.not.exists");
    ErrorCode USER_MOBILE_NOT_EXISTS = new ErrorCode(1_004_001_001, "member.user.mobile.not.exists");
    ErrorCode USER_MOBILE_USED = new ErrorCode(1_004_001_002, "member.user.mobile.used");
    ErrorCode USER_POINT_NOT_ENOUGH = new ErrorCode(1_004_001_003, "member.user.point.not.enough");
    ErrorCode USER_EMAIL_NOT_EXISTS = new ErrorCode(1_004_001_001, "member.user.email.not.exists");
    ErrorCode USER_CHANGE_PASSWORD_NOT_MATCH = new ErrorCode(1_004_001_001, "member.user.change.password.not.match");

    // ========== AUTH 模块 1-004-003-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_004_003_000, "member.auth.login.bad.credentials");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_004_003_001, "member.auth.login.user.disabled");
    ErrorCode AUTH_SOCIAL_USER_NOT_FOUND = new ErrorCode(1_004_003_005, "member.auth.social.user.not.found");
    ErrorCode AUTH_MOBILE_USED = new ErrorCode(1_004_003_007, "member.auth.mobile.used");

    // ========== 用户收件地址 1-004-004-000 ==========
    ErrorCode ADDRESS_NOT_EXISTS = new ErrorCode(1_004_004_000, "member.address.not.exists");

    // ========== 用户标签 1-004-006-000 ==========
    ErrorCode TAG_NOT_EXISTS = new ErrorCode(1_004_006_000, "member.tag.not.exists");
    ErrorCode TAG_NAME_EXISTS = new ErrorCode(1_004_006_001, "member.tag.name.exists");
    ErrorCode TAG_HAS_USER = new ErrorCode(1_004_006_002, "member.tag.has.user");

    // ========== 积分记录 1-004-008-000 ==========
    ErrorCode POINT_RECORD_BIZ_NOT_SUPPORT = new ErrorCode(1_004_008_000, "member.point.record.biz.not.support");

    // ========== 签到配置 1-004-009-000 ==========
    ErrorCode SIGN_IN_CONFIG_NOT_EXISTS = new ErrorCode(1_004_009_000, "member.sign.in.config.not.exists");
    ErrorCode SIGN_IN_CONFIG_EXISTS = new ErrorCode(1_004_009_001, "member.sign.in.config.exists");

    // ========== 签到记录 1-004-010-000 ==========
    ErrorCode SIGN_IN_RECORD_TODAY_EXISTS = new ErrorCode(1_004_010_000, "member.sign.in.record.today.exists");

    // ========== 用户等级 1-004-011-000 ==========
    ErrorCode LEVEL_NOT_EXISTS = new ErrorCode(1_004_011_000, "member.level.not.exists");
    ErrorCode LEVEL_NAME_EXISTS = new ErrorCode(1_004_011_001, "member.level.name.exists");
    ErrorCode LEVEL_VALUE_EXISTS = new ErrorCode(1_004_011_002, "member.level.value.exists");
    ErrorCode LEVEL_EXPERIENCE_MIN = new ErrorCode(1_004_011_003, "member.level.experience.min");
    ErrorCode LEVEL_EXPERIENCE_MAX = new ErrorCode(1_004_011_004, "member.level.experience.max");
    ErrorCode LEVEL_HAS_USER = new ErrorCode(1_004_011_005, "member.level.has.user");

    ErrorCode EXPERIENCE_BIZ_NOT_SUPPORT = new ErrorCode(1_004_011_201, "member.experience.biz.not.support");

    // ========== 用户分组 1-004-012-000 ==========
    ErrorCode GROUP_NOT_EXISTS = new ErrorCode(1_004_012_000, "member.group.not.exists");
    ErrorCode GROUP_HAS_USER = new ErrorCode(1_004_012_001, "member.group.has.user");

    // ========== 邮箱验证 1-004-013-000 ==========
    ErrorCode EMAIL_VERIFY_NOT_EXISTS = new ErrorCode(1_004_013_000, "member.email.verify.not.exists");
    ErrorCode EMAIL_VERIFY_EXPIRED = new ErrorCode(1_004_013_001, "member.email.verify.expired");
    ErrorCode EMAIL_VERIFY_ERROR = new ErrorCode(1_004_013_002, "member.email.verify.error");
    ErrorCode EMAIL_VERIFY_TOKEN_INVALID_OR_EXPIRED = new ErrorCode(1_004_013_003, "member.email.verify.token.invalid.expired");
    ErrorCode EMAIL_VERIFY_ALREADY_VERIFIED = new ErrorCode(1_004_013_004, "member.email.verify.already.verified");
    ErrorCode EMAIL_VERIFY_WAIT = new ErrorCode(1_004_013_005, "member.email.verify.wait");

    // ========== 订阅 1-004-014-000 ==========
    ErrorCode SUBSCRIBE_LINK_EXPIRED = new ErrorCode(1_004_014_000, "member.subscribe.link.expired");

    // ========== 用户咨询 1-004-015-000 ==========
    ErrorCode INQUIRY_NOT_EXISTS = new ErrorCode(1_004_015_000, "member.inquiry.not.exists");

    // ========== 用户工单 1-004-015-000 ==========
    ErrorCode TICKET_NOT_EXISTS = new ErrorCode(1_004_016_000, "member.ticket.not.exists");


}
