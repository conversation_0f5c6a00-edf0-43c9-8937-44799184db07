package cn.iocoder.yudao.module.member.enums.ticket;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum MemberTicketPriorityEnum implements IntArrayValuable {
    HIGH(1, "高"),
    MEDIUM(2, "中"),
    LOW(3, "低");

    private final Integer priority;
    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(MemberTicketPriorityEnum::getPriority).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
