package cn.iocoder.yudao.module.member.enums.ticket;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum MemberTicketPriorityEnum implements IntArrayValuable {
    HIGH(1, "高"),
    MEDIUM(2, "中"),
    LOW(3, "低");

    private final Integer priority;
    private final String name;

    @Override
    public int[] array() {
        return new int[0];
    }
}
