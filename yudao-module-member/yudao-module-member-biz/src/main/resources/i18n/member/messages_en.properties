member.user.not.exists=User does not exist
member.user.mobile.not.exists=Mobile number is not registered
member.user.mobile.used=Failed to change mobile number, the number ({0}) is already in use
member.user.point.not.enough=Insufficient user points balance
member.user.email.not.exists=Email is not registered
member.user.change.password.not.match=Incorrect old password

member.auth.login.bad.credentials=Login failed, incorrect username or password
member.auth.login.user.disabled=Login failed, account is disabled
member.auth.social.user.not.found=Login failed, unable to parse third-party login information
member.auth.mobile.used=Mobile number is already in use

member.address.not.exists=User address does not exist

member.tag.not.exists=User tag does not exist
member.tag.name.exists=User tag already exists
member.tag.has.user=User tag has associated users and cannot be deleted

member.point.record.biz.not.support=Unsupported user point record business type

member.sign.in.config.not.exists=Sign-in rule does not exist
member.sign.in.config.exists=Sign-in rule already exists

member.sign.in.record.today.exists=You have already signed in today, please do not sign in again

member.level.not.exists=User level does not exist
member.level.name.exists=User level name [{0}] is already in use
member.level.value.exists=User level value [{0}] is already used by [{1}]
member.level.experience.min=Upgrade experience must be greater than the experience set for the previous level [{0}] [{1}]
member.level.experience.max=Upgrade experience must be less than the experience set for the next level [{0}] [{1}]
member.level.has.user=User level has associated users and cannot be deleted

member.experience.biz.not.support=Unsupported user experience business type

member.group.not.exists=User group does not exist
member.group.has.user=User group has associated users and cannot be deleted

member.email.verify.not.exists=User does not exist.
member.email.verify.expired=The link has expired.
member.email.verify.error=Email verification failed.
member.email.verify.token.invalid.expired=The link is invalid or expired.
member.email.verify.already.verified=Email verified
member.email.verify.wait=Sending too frequently, please wait a minute and try again

member.subscribe.link.expired=The unsubscribe link has expired or invalid. If you wish to continue unsubscribing, please visit the subscription management page to proceed.

member.inquiry.not.exists=Inquiry does not exist.

member.ticket.not.exists=Ticket does not exist.