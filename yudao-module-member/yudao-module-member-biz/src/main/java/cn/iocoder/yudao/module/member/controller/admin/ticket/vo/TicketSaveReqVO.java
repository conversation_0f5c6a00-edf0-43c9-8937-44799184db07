package cn.iocoder.yudao.module.member.controller.admin.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage.TicketMessageDO;

@Schema(description = "管理后台 - 用户工单新增/修改 Request VO")
@Data
public class TicketSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "25623")
    private Long id;

    @Schema(description = "工单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "工单号不能为空")
    private String no;

    @Schema(description = "工单标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "工单标题不能为空")
    private String title;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22871")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    @Schema(description = "附件URL列表")
    private List<String> attachmentUrls;

    @Schema(description = "问题描述", example = "你猜")
    private String description;

    @Schema(description = "管理员编号", example = "4290")
    private Long adminId;

    @Schema(description = "用户评分;1-5分")
    private Integer rating;

    @Schema(description = "评分备注")
    private String ratingComment;

    @Schema(description = "关闭时间")
    private LocalDateTime closedTime;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "用户工单信息列表")
    private List<TicketMessageDO> ticketMessages;

}