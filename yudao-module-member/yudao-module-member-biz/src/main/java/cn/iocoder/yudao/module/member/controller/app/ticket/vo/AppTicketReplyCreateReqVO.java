package cn.iocoder.yudao.module.member.controller.app.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "用户 APP - 创建工单回复 Request VO")
@Data
public class AppTicketReplyCreateReqVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "工单编号不能为空")
    private Long ticketId;

    @Schema(description = "回复内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "感谢您的回复，问题已解决")
    @NotBlank(message = "回复内容不能为空")
    private String content;

    @Schema(description = "附件URL列表", example = "[\"https://example.com/file1.jpg\", \"https://example.com/file2.pdf\"]")
    private List<String> attachmentUrls;

}
