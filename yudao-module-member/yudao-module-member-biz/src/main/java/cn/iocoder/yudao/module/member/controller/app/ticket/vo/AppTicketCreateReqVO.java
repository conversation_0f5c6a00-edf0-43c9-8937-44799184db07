package cn.iocoder.yudao.module.member.controller.app.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "用户 APP - 创建工单 Request VO")
@Data
public class AppTicketCreateReqVO {

    @Schema(description = "工单标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "订单支付问题")
    @NotBlank(message = "工单标题不能为空")
    @Length(max = 200, message = "工单标题长度不能超过200个字符")
    private String title;

    @Schema(description = "工单类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "工单类型不能为空")
    private Integer type;

    @Schema(description = "优先级", example = "2")
    private Integer priority;

    @Schema(description = "问题描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "订单支付后未到账，请帮忙处理")
    @NotBlank(message = "问题描述不能为空")
    private String description;

    @Schema(description = "附件URL列表", example = "[\"https://example.com/file1.jpg\", \"https://example.com/file2.pdf\"]")
    private List<String> attachmentUrls;

}
