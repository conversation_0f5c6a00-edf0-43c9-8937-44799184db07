package cn.iocoder.yudao.module.member.service.ticket;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.member.controller.admin.ticket.vo.*;
import cn.iocoder.yudao.module.member.dal.dataobject.ticket.TicketDO;
import cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage.TicketMessageDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 用户工单 Service 接口
 *
 * <AUTHOR>
 */
public interface TicketService {

    /**
     * 创建用户工单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTicket(@Valid TicketSaveReqVO createReqVO);

    /**
     * 更新用户工单
     *
     * @param updateReqVO 更新信息
     */
    void updateTicket(@Valid TicketSaveReqVO updateReqVO);

    /**
     * 删除用户工单
     *
     * @param id 编号
     */
    void deleteTicket(Long id);

    /**
     * 获得用户工单
     *
     * @param id 编号
     * @return 用户工单
     */
    TicketDO getTicket(Long id);

    /**
     * 获得用户工单分页
     *
     * @param pageReqVO 分页查询
     * @return 用户工单分页
     */
    PageResult<TicketDO> getTicketPage(TicketPageReqVO pageReqVO);

    // ==================== 子表（用户工单信息） ====================

    /**
     * 获得用户工单信息列表
     *
     * @param ticketId 工单编号
     * @return 用户工单信息列表
     */
    List<TicketMessageDO> getTicketMessageListByTicketId(Long ticketId);

}