package cn.iocoder.yudao.module.member.controller.app.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 APP - 工单回复 Response VO")
@Data
public class AppTicketMessageRespVO {

    @Schema(description = "回复编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private Long ticketId;

    @Schema(description = "回复类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer messageType;

    @Schema(description = "回复类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "用户回复")
    private String messageTypeName;

    @Schema(description = "回复内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "感谢您的回复，问题已解决")
    private String content;

    @Schema(description = "附件URL列表")
    private List<String> attachmentUrls;

    @Schema(description = "回复人编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long replierId;

    @Schema(description = "回复人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String replierName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
