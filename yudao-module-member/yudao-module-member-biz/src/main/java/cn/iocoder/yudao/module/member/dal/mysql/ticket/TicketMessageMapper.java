package cn.iocoder.yudao.module.member.dal.mysql.ticketmessage;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage.TicketMessageDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户工单信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TicketMessageMapper extends BaseMapperX<TicketMessageDO> {

    default List<TicketMessageDO> selectListByTicketId(Long ticketId) {
        return selectList(TicketMessageDO::getTicketId, ticketId);
    }

    default int deleteByTicketId(Long ticketId) {
        return delete(TicketMessageDO::getTicketId, ticketId);
    }

}