package cn.iocoder.yudao.module.member.dal.dataobject.ticket;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.member.enums.ticket.MemberTicketPriorityEnum;
import cn.iocoder.yudao.module.member.enums.ticket.MemberTicketStatusEnum;
import cn.iocoder.yudao.module.member.enums.ticket.MemberTicketTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户工单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "member_ticket", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 工单号
     */
    private String no;
    /**
     * 工单标题
     */
    private String title;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 类型
     *
     * 枚举 {@link MemberTicketTypeEnum}
     */
    private Integer type;
    /**
     * 优先级
     *
     * 枚举 {@link MemberTicketPriorityEnum}
     */
    private Integer priority;
    /**
     * 附件URL列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachmentUrls;
    /**
     * 问题描述
     */
    private String description;
    /**
     * 管理员客服编号
     */
    private Long adminId;
    /**
     * 用户评分;1-5分
     */
    private Integer rating;
    /**
     * 评分备注
     */
    private String ratingComment;
    /**
     * 关闭时间
     */
    private LocalDateTime closedTime;
    /**
     * 状态
     *
     * 枚举 {@link MemberTicketStatusEnum}
     */
    private Integer status;

}