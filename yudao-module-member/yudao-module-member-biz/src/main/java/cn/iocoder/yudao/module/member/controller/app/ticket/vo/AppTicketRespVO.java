package cn.iocoder.yudao.module.member.controller.app.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 APP - 工单 Response VO")
@Data
public class AppTicketRespVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "工单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "T202401010001")
    private String no;

    @Schema(description = "工单标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "订单支付问题")
    private String title;

    @Schema(description = "工单类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "工单类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "订单问题")
    private String typeName;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer priority;

    @Schema(description = "优先级名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "中")
    private String priorityName;

    @Schema(description = "工单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "工单状态名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "待处理")
    private String statusName;

    @Schema(description = "问题描述", example = "订单支付后未到账，请帮忙处理")
    private String description;

    @Schema(description = "附件URL列表")
    private List<String> attachmentUrls;

    @Schema(description = "管理员编号", example = "1001")
    private Long adminId;

    @Schema(description = "管理员名称", example = "客服小王")
    private String adminName;

    @Schema(description = "用户评分", example = "5")
    private Integer rating;

    @Schema(description = "评分备注", example = "服务很好")
    private String ratingComment;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "关闭时间")
    private LocalDateTime closedTime;

}
