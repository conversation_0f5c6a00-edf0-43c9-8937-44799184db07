package cn.iocoder.yudao.module.member.dal.mysql.ticket;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.member.dal.dataobject.ticket.TicketDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.member.controller.admin.ticket.vo.*;

/**
 * 用户工单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TicketMapper extends BaseMapperX<TicketDO> {

    default PageResult<TicketDO> selectPage(TicketPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TicketDO>()
                .eqIfPresent(TicketDO::getNo, reqVO.getNo())
                .eqIfPresent(TicketDO::getUserId, reqVO.getUserId())
                .eqIfPresent(TicketDO::getType, reqVO.getType())
                .eqIfPresent(TicketDO::getPriority, reqVO.getPriority())
                .betweenIfPresent(TicketDO::getClosedTime, reqVO.getClosedTime())
                .eqIfPresent(TicketDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TicketDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TicketDO::getId));
    }

}