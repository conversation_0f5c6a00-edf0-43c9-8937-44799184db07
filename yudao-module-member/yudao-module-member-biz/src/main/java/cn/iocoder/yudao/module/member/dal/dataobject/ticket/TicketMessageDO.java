package cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.member.enums.ticket.MemberTicketMessageTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.util.List;

/**
 * 用户工单信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "member_ticket_message", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketMessageDO extends BaseDO {

    /**
     * 信息编号
     */
    @TableId
    private Long id;
    /**
     * 工单编号
     */
    private Long ticketId;
    /**
     * 回复类型
     *
     * 枚举 {@link MemberTicketMessageTypeEnum}
     */
    private Integer messageType;
    /**
     * 回复内容
     */
    private String content;
    /**
     * 附件URL列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachmentUrls;
    /**
     * 回复人编号
     */
    private Long replierId;
    /**
     * 回复人名称
     */
    private String replierName;

}