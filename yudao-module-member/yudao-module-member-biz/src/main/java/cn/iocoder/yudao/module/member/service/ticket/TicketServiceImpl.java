package cn.iocoder.yudao.module.member.service.ticket;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.member.controller.admin.ticket.vo.*;
import cn.iocoder.yudao.module.member.dal.dataobject.ticket.TicketDO;
import cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage.TicketMessageDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.member.dal.mysql.ticket.TicketMapper;
import cn.iocoder.yudao.module.member.dal.mysql.ticketmessage.TicketMessageMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.*;

/**
 * 用户工单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TicketServiceImpl implements TicketService {

    @Resource
    private TicketMapper ticketMapper;
    @Resource
    private TicketMessageMapper ticketMessageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTicket(TicketSaveReqVO createReqVO) {
        // 插入
        TicketDO ticket = BeanUtils.toBean(createReqVO, TicketDO.class);
        ticketMapper.insert(ticket);

        // 插入子表
        createTicketMessageList(ticket.getId(), createReqVO.getTicketMessages());
        // 返回
        return ticket.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTicket(TicketSaveReqVO updateReqVO) {
        // 校验存在
        validateTicketExists(updateReqVO.getId());
        // 更新
        TicketDO updateObj = BeanUtils.toBean(updateReqVO, TicketDO.class);
        ticketMapper.updateById(updateObj);

        // 更新子表
        updateTicketMessageList(updateReqVO.getId(), updateReqVO.getTicketMessages());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTicket(Long id) {
        // 校验存在
        validateTicketExists(id);
        // 删除
        ticketMapper.deleteById(id);

        // 删除子表
        deleteTicketMessageByTicketId(id);
    }

    private void validateTicketExists(Long id) {
        if (ticketMapper.selectById(id) == null) {
            throw exception(TICKET_NOT_EXISTS);
        }
    }

    @Override
    public TicketDO getTicket(Long id) {
        return ticketMapper.selectById(id);
    }

    @Override
    public PageResult<TicketDO> getTicketPage(TicketPageReqVO pageReqVO) {
        return ticketMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（用户工单信息） ====================

    @Override
    public List<TicketMessageDO> getTicketMessageListByTicketId(Long ticketId) {
        return ticketMessageMapper.selectListByTicketId(ticketId);
    }

    private void createTicketMessageList(Long ticketId, List<TicketMessageDO> list) {
        list.forEach(o -> o.setTicketId(ticketId));
        ticketMessageMapper.insertBatch(list);
    }

    private void updateTicketMessageList(Long ticketId, List<TicketMessageDO> list) {
        deleteTicketMessageByTicketId(ticketId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createTicketMessageList(ticketId, list);
    }

    private void deleteTicketMessageByTicketId(Long ticketId) {
        ticketMessageMapper.deleteByTicketId(ticketId);
    }

}