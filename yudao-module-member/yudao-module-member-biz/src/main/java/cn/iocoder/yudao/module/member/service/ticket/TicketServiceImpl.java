package cn.iocoder.yudao.module.member.service.ticket;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.member.controller.app.ticket.vo.*;
import cn.iocoder.yudao.module.member.enums.ticket.*;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import cn.iocoder.yudao.module.member.controller.admin.ticket.vo.*;
import cn.iocoder.yudao.module.member.dal.dataobject.ticket.TicketDO;
import cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage.TicketMessageDO;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.member.dal.mysql.ticket.TicketMapper;
import cn.iocoder.yudao.module.member.dal.mysql.ticketmessage.TicketMessageMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.*;

/**
 * 用户工单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TicketServiceImpl implements TicketService {

    @Resource
    private TicketMapper ticketMapper;
    @Resource
    private TicketMessageMapper ticketMessageMapper;
    @Resource
    private MemberUserService memberUserService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTicket(TicketSaveReqVO createReqVO) {
        // 插入
        TicketDO ticket = BeanUtils.toBean(createReqVO, TicketDO.class);
        ticketMapper.insert(ticket);

        // 插入子表
        createTicketMessageList(ticket.getId(), createReqVO.getTicketMessages());
        // 返回
        return ticket.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTicket(TicketSaveReqVO updateReqVO) {
        // 校验存在
        validateTicketExists(updateReqVO.getId());
        // 更新
        TicketDO updateObj = BeanUtils.toBean(updateReqVO, TicketDO.class);
        ticketMapper.updateById(updateObj);

        // 更新子表
        updateTicketMessageList(updateReqVO.getId(), updateReqVO.getTicketMessages());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTicket(Long id) {
        // 校验存在
        validateTicketExists(id);
        // 删除
        ticketMapper.deleteById(id);

        // 删除子表
        deleteTicketMessageByTicketId(id);
    }

    private void validateTicketExists(Long id) {
        if (ticketMapper.selectById(id) == null) {
            throw exception(TICKET_NOT_EXISTS);
        }
    }

    @Override
    public TicketDO getTicket(Long id) {
        return ticketMapper.selectById(id);
    }

    @Override
    public PageResult<TicketDO> getTicketPage(TicketPageReqVO pageReqVO) {
        return ticketMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（用户工单信息） ====================

    @Override
    public List<TicketMessageDO> getTicketMessageListByTicketId(Long ticketId) {
        return ticketMessageMapper.selectListByTicketId(ticketId);
    }

    private void createTicketMessageList(Long ticketId, List<TicketMessageDO> list) {
        list.forEach(o -> o.setTicketId(ticketId));
        ticketMessageMapper.insertBatch(list);
    }

    private void updateTicketMessageList(Long ticketId, List<TicketMessageDO> list) {
        deleteTicketMessageByTicketId(ticketId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createTicketMessageList(ticketId, list);
    }

    private void deleteTicketMessageByTicketId(Long ticketId) {
        ticketMessageMapper.deleteByTicketId(ticketId);
    }

    // ==================== App端接口实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAppTicket(Long userId, AppTicketCreateReqVO createReqVO) {
        // 校验用户存在
        MemberUserDO user = validateUserExists(userId);

        // 创建工单
        TicketDO ticket = BeanUtils.toBean(createReqVO, TicketDO.class);
        ticket.setUserId(userId);
        ticket.setNo(generateTicketNo());
        ticket.setStatus(MemberTicketStatusEnum.PENDING.getStatus());
        if (ticket.getPriority() == null) {
            ticket.setPriority(MemberTicketPriorityEnum.MEDIUM.getPriority());
        }
        ticketMapper.insert(ticket);

        // 创建初始消息（用户提交的问题描述）
        TicketMessageDO message = new TicketMessageDO();
        message.setTicketId(ticket.getId());
        message.setMessageType(MemberTicketMessageTypeEnum.USER.getType());
        message.setContent(createReqVO.getDescription());
        message.setAttachmentUrls(createReqVO.getAttachmentUrls());
        message.setReplierId(userId);
        message.setReplierName(user.getNickname());
        ticketMessageMapper.insert(message);

        return ticket.getId();
    }

    @Override
    public PageResult<TicketDO> getAppTicketPage(Long userId, AppTicketPageReqVO pageReqVO) {
        return ticketMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<TicketDO>()
                .eq(TicketDO::getUserId, userId)
                .eqIfPresent(TicketDO::getType, pageReqVO.getType())
                .eqIfPresent(TicketDO::getStatus, pageReqVO.getStatus())
                .and(StrUtil.isNotBlank(pageReqVO.getKeyword()), w -> w
                        .like(TicketDO::getTitle, pageReqVO.getKeyword())
                        .or().like(TicketDO::getDescription, pageReqVO.getKeyword()))
                .betweenIfPresent(TicketDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(TicketDO::getId));
    }

    @Override
    public AppTicketDetailRespVO getAppTicketDetail(Long userId, Long id) {
        // 校验工单存在且属于当前用户
        TicketDO ticket = validateTicketOwnership(userId, id);

        // 转换基本信息
        AppTicketDetailRespVO respVO = BeanUtils.toBean(ticket, AppTicketDetailRespVO.class);
        enrichTicketInfo(respVO);

        // 获取回复列表
        List<TicketMessageDO> messages = ticketMessageMapper.selectListByTicketId(id);
        List<AppTicketMessageRespVO> messageRespVOs = BeanUtils.toBean(messages, AppTicketMessageRespVO.class);
        messageRespVOs.forEach(this::enrichMessageInfo);
        respVO.setMessages(messageRespVOs);

        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAppTicketReply(Long userId, AppTicketReplyCreateReqVO createReqVO) {
        // 校验工单存在且属于当前用户
        TicketDO ticket = validateTicketOwnership(userId, createReqVO.getTicketId());

        // 校验工单状态（已关闭的工单不能回复）
        if (MemberTicketStatusEnum.CLOSED.getStatus().equals(ticket.getStatus())) {
            throw exception(TICKET_CLOSED_CANNOT_REPLY);
        }

        // 获取用户信息
        MemberUserDO user = memberUserService.getUser(userId);

        // 创建回复
        TicketMessageDO message = new TicketMessageDO();
        message.setTicketId(createReqVO.getTicketId());
        message.setMessageType(MemberTicketMessageTypeEnum.USER.getType());
        message.setContent(createReqVO.getContent());
        message.setAttachmentUrls(createReqVO.getAttachmentUrls());
        message.setReplierId(userId);
        message.setReplierName(user.getNickname());
        ticketMessageMapper.insert(message);

        // 更新工单状态为处理中（如果当前是待用户回复状态）
        if (MemberTicketStatusEnum.WAITING_USER.getStatus().equals(ticket.getStatus())) {
            ticketMapper.updateById(new TicketDO().setId(ticket.getId())
                    .setStatus(MemberTicketStatusEnum.PROCESSING.getStatus()));
        }

        return message.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeAppTicket(Long userId, Long ticketId) {
        // 校验工单存在且属于当前用户
        TicketDO ticket = validateTicketOwnership(userId, ticketId);

        // 校验工单状态（已关闭的工单不能再次关闭）
        if (MemberTicketStatusEnum.CLOSED.getStatus().equals(ticket.getStatus())) {
            throw exception(TICKET_ALREADY_CLOSED);
        }

        // 更新工单状态为已关闭
        ticketMapper.updateById(new TicketDO().setId(ticketId)
                .setStatus(MemberTicketStatusEnum.CLOSED.getStatus())
                .setClosedTime(LocalDateTime.now()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rateAppTicket(Long userId, AppTicketRateReqVO rateReqVO) {
        // 校验工单存在且属于当前用户
        TicketDO ticket = validateTicketOwnership(userId, rateReqVO.getTicketId());

        // 校验工单状态（只有已解决或已关闭的工单才能评分）
        if (!MemberTicketStatusEnum.RESOLVED.getStatus().equals(ticket.getStatus())
                && !MemberTicketStatusEnum.CLOSED.getStatus().equals(ticket.getStatus())) {
            throw exception(TICKET_CANNOT_RATE);
        }

        // 更新评分
        ticketMapper.updateById(new TicketDO().setId(rateReqVO.getTicketId())
                .setRating(rateReqVO.getRating())
                .setRatingComment(rateReqVO.getRatingComment()));
    }

    @Override
    public List<AppTicketTypeRespVO> getTicketTypes() {
        List<AppTicketTypeRespVO> result = new ArrayList<>();
        for (MemberTicketTypeEnum typeEnum : MemberTicketTypeEnum.values()) {
            AppTicketTypeRespVO respVO = new AppTicketTypeRespVO();
            respVO.setType(typeEnum.getType());
            respVO.setName(typeEnum.getName());
            result.add(respVO);
        }
        return result;
    }

    // ==================== 辅助方法 ====================

    /**
     * 生成工单号
     */
    private String generateTicketNo() {
        String prefix = "T";
        String noPrefix = prefix + DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        String key = "ticket:no:" + noPrefix;
        Long no = stringRedisTemplate.opsForValue().increment(key);
        // 设置过期时间为1天
        stringRedisTemplate.expire(key, Duration.ofDays(1L));
        return noPrefix + String.format("%04d", no);
    }

    /**
     * 校验用户存在
     */
    private MemberUserDO validateUserExists(Long userId) {
        MemberUserDO user = memberUserService.getUser(userId);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    /**
     * 校验工单归属权
     */
    private TicketDO validateTicketOwnership(Long userId, Long ticketId) {
        TicketDO ticket = ticketMapper.selectById(ticketId);
        if (ticket == null) {
            throw exception(TICKET_NOT_EXISTS);
        }
        if (!userId.equals(ticket.getUserId())) {
            throw exception(TICKET_ACCESS_DENIED);
        }
        return ticket;
    }

    /**
     * 丰富工单信息（添加枚举名称）
     */
    private void enrichTicketInfo(AppTicketRespVO respVO) {
        // 设置类型名称
        MemberTicketTypeEnum typeEnum = Arrays.stream(MemberTicketTypeEnum.values())
                .filter(e -> e.getType().equals(respVO.getType()))
                .findFirst().orElse(null);
        if (typeEnum != null) {
            respVO.setTypeName(typeEnum.getName());
        }

        // 设置状态名称
        MemberTicketStatusEnum statusEnum = Arrays.stream(MemberTicketStatusEnum.values())
                .filter(e -> e.getStatus().equals(respVO.getStatus()))
                .findFirst().orElse(null);
        if (statusEnum != null) {
            respVO.setStatusName(statusEnum.getName());
        }

        // 设置优先级名称
        MemberTicketPriorityEnum priorityEnum = Arrays.stream(MemberTicketPriorityEnum.values())
                .filter(e -> e.getPriority().equals(respVO.getPriority()))
                .findFirst().orElse(null);
        if (priorityEnum != null) {
            respVO.setPriorityName(priorityEnum.getName());
        }
    }

    /**
     * 丰富消息信息（添加枚举名称）
     */
    private void enrichMessageInfo(AppTicketMessageRespVO respVO) {
        // 设置消息类型名称
        MemberTicketMessageTypeEnum messageTypeEnum = Arrays.stream(MemberTicketMessageTypeEnum.values())
                .filter(e -> e.getType().equals(respVO.getMessageType()))
                .findFirst().orElse(null);
        if (messageTypeEnum != null) {
            respVO.setMessageTypeName(messageTypeEnum.getName());
        }
    }

}