package cn.iocoder.yudao.module.member.controller.app.ticket;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.member.controller.app.ticket.vo.*;
import cn.iocoder.yudao.module.member.dal.dataobject.ticket.TicketDO;
import cn.iocoder.yudao.module.member.dal.dataobject.ticketmessage.TicketMessageDO;
import cn.iocoder.yudao.module.member.enums.ticket.MemberTicketTypeEnum;
import cn.iocoder.yudao.module.member.service.ticket.TicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 工单管理")
@RestController
@RequestMapping("/member/ticket")
@Validated
@Slf4j
public class AppTicketController {

    @Resource
    private TicketService ticketService;

    @PostMapping("/create")
    @Operation(summary = "创建工单")
    @PreAuthenticated
    public CommonResult<Long> createTicket(@Valid @RequestBody AppTicketCreateReqVO createReqVO) {
        return success(ticketService.createAppTicket(getLoginUserId(), createReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获取工单分页列表")
    @PreAuthenticated
    public CommonResult<PageResult<AppTicketRespVO>> getTicketPage(@Valid AppTicketPageReqVO pageReqVO) {
        PageResult<TicketDO> pageResult = ticketService.getAppTicketPage(getLoginUserId(), pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppTicketRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获取工单详情")
    @Parameter(name = "id", description = "工单编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<AppTicketDetailRespVO> getTicket(@RequestParam("id") Long id) {
        return success(ticketService.getAppTicketDetail(getLoginUserId(), id));
    }

    @PostMapping("/reply")
    @Operation(summary = "提交回复")
    @PreAuthenticated
    public CommonResult<Long> replyTicket(@Valid @RequestBody AppTicketReplyCreateReqVO createReqVO) {
        return success(ticketService.createAppTicketReply(getLoginUserId(), createReqVO));
    }

    @PutMapping("/close/{id}")
    @Operation(summary = "关闭工单")
    @Parameter(name = "id", description = "工单编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<Boolean> closeTicket(@PathVariable("id") Long id) {
        ticketService.closeAppTicket(getLoginUserId(), id);
        return success(true);
    }

    @PutMapping("/rate")
    @Operation(summary = "评分工单")
    @PreAuthenticated
    public CommonResult<Boolean> rateTicket(@Valid @RequestBody AppTicketRateReqVO rateReqVO) {
        ticketService.rateAppTicket(getLoginUserId(), rateReqVO);
        return success(true);
    }

    @GetMapping("/types")
    @Operation(summary = "获取工单类型列表")
    public CommonResult<List<AppTicketTypeRespVO>> getTicketTypes() {
        return success(ticketService.getTicketTypes());
    }

}
