package cn.iocoder.yudao.module.member.controller.admin.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 用户工单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TicketRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "25623")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "工单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("工单号")
    private String no;

    @Schema(description = "工单标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("工单标题")
    private String title;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22871")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "类型", converter = DictConvert.class)
    @DictFormat("ticket_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer type;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "优先级", converter = DictConvert.class)
    @DictFormat("ticket_priority") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer priority;

    @Schema(description = "管理员编号", example = "4290")
    @ExcelProperty("管理员编号")
    private Long adminId;

    @Schema(description = "用户评分;1-5分")
    @ExcelProperty("用户评分;1-5分")
    private Integer rating;

    @Schema(description = "关闭时间")
    @ExcelProperty("关闭时间")
    private LocalDateTime closedTime;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("ticket_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}