package cn.iocoder.yudao.module.member.controller.app.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 工单类型 Response VO")
@Data
public class AppTicketTypeRespVO {

    @Schema(description = "类型值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "订单问题")
    private String name;

}
