package cn.iocoder.yudao.module.member.controller.app.ticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Schema(description = "用户 APP - 工单详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppTicketDetailRespVO extends AppTicketRespVO {

    @Schema(description = "回复列表")
    private List<AppTicketMessageRespVO> messages;

}
