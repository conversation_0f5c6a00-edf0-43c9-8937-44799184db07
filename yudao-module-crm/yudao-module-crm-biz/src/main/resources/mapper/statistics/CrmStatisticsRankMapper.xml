<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.crm.dal.mysql.statistics.CrmStatisticsRankMapper">

    <select id="selectContractPriceRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT IFNULL(SUM(total_price), 0) AS count, owner_user_id
        FROM crm_contract
        WHERE deleted = 0
        AND audit_status = ${@<EMAIL>}
        and owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND order_date between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY owner_user_id
    </select>

    <select id="selectReceivablePriceRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT IFNULL(SUM(price), 0) AS count, owner_user_id
        FROM crm_receivable
        WHERE deleted = 0
        AND audit_status = ${@<EMAIL>}
        AND owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND return_time between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY owner_user_id
    </select>

    <select id="selectContractCountRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT COUNT(1) AS count, owner_user_id
        FROM crm_contract
        WHERE deleted = 0
        AND audit_status = ${@<EMAIL>}
        AND owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND order_date between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY owner_user_id
    </select>

    <select id="selectProductSalesRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT COUNT(product.count) AS count, contract.owner_user_id
        FROM crm_contract_product product
        INNER JOIN crm_contract contract ON product.contract_id = contract.id
        WHERE contract.deleted = 0 AND contract.deleted = 0
        AND contract.audit_status = ${@<EMAIL>}
        AND contract.owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND contract.order_date between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY contract.owner_user_id
    </select>

    <select id="selectCustomerCountRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT COUNT(1) AS count, owner_user_id
        FROM crm_customer
        WHERE deleted = 0
        AND owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND create_time between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY owner_user_id
    </select>

    <select id="selectContactsCountRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT COUNT(1) AS count, owner_user_id
        FROM crm_contact
        WHERE deleted = 0
        AND owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND create_time between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY owner_user_id
    </select>

    <select id="selectFollowCountRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT COUNT(1) AS count, cc.owner_user_id
        FROM crm_follow_up_record AS cfur
        LEFT JOIN crm_contact AS cc ON FIND_IN_SET(cc.id, cfur.contact_ids)
        WHERE cfur.deleted = 0
        AND cc.deleted = 0
        AND cc.owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND cfur.create_time between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY cc.owner_user_id
    </select>

    <select id="selectFollowCustomerCountRank"
            resultType="cn.iocoder.yudao.module.crm.controller.admin.statistics.vo.rank.CrmStatisticsRankRespVO">
        SELECT COUNT(DISTINCT cc.id) AS count, cc.owner_user_id
        FROM crm_follow_up_record AS cfur
        LEFT JOIN crm_contact AS cc ON FIND_IN_SET(cc.id, cfur.contact_ids)
        WHERE cfur.deleted = 0
        AND cc.deleted = 0
        AND cc.owner_user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        AND cfur.create_time between #{times[0],javaType=java.time.LocalDateTime} and #{times[1],javaType=java.time.LocalDateTime}
        GROUP BY cc.owner_user_id
    </select>

</mapper>
